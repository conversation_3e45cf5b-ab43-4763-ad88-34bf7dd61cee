<?php

namespace App\Http\Controllers;

use App\Models\Holdings;
use Illuminate\Http\Request;
use TomorrowIdeas\Plaid\Entities\User;
use TomorrowIdeas\Plaid\Plaid;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use App\Models\PlaidAccounts;
use App\Models\BankAccount;
use TomorrowIdeas\Plaid\PlaidRequestException;
// use App\Models\Holdings;

class PlaidController extends Controller
{

    protected Plaid $plaid;

    public function __construct()
    {
        $this->plaid = new Plaid(
            env('PLAID_CLIENT_ID'),
            env('PLAID_SECRET'),
            env('PLAID_ENV')
        );
    }
    public function createLinkToken()
    {
        $user_id = Auth::user()->id;
        $plaidUser = new User($user_id);
        $plaid = new Plaid(env('PLAID_CLIENT_ID'), env('PLAID_SECRET'), env('PLAID_ENV'));
        $response = $plaid->tokens->create('Plaid Test', 'en', ['US'], $plaidUser, ['investments'], env('PLAID_WEBHOOK'));
        return response()->json([
            'result' => 'success',
            'data' => json_encode($response)
        ], 200);
    }

    public function storePlaidAccount(Request $request)
    {
        $validator = \Validator::make($request->all(), [
            'public_token' => ['required', 'string']
        ]);

        if ($validator->fails()) {
            return response()->json(['result' => 'error', 'message' => $validator->errors()], 201);
        }

        $user_id = Auth::user()->id;
        $plaid = new Plaid(env('PLAID_CLIENT_ID'), env('PLAID_SECRET'), env('PLAID_ENV'));
        $obj = $plaid->items->exchangeToken($request->public_token);
        try {
            \DB::transaction(function () use ($request, $obj, $user_id) {
                foreach ($request->accounts as $account) {
                    $query = BankAccount::where('account_id', isset($account['id']) ? $account['id'] : $account['account_id']);
                    if ($query->count() > 0) {
                        $new_account = $query->first();
                        $new_account->plaid_item_id = $obj->item_id;
                        $new_account->plaid_access_token = $obj->access_token;
                        $new_account->plaid_public_token = $request->public_token;
                        $new_account->link_session_id = $request->link_session_id;
                        $new_account->link_token = $request->link_token;
                        $new_account->institution_id = $request->institution['institution_id'];
                        $new_account->institution_name = $request->institution['name'];
                        $new_account->account_id = isset($account['id']) ? $account['id'] : $account['account_id'];
                        $new_account->account_name = isset($account['name']) ? $account['name'] : $account['account_name'];
                        $new_account->account_mask = isset($account['account_number']) ? $account['account_number'] : $account['mask'];
                        $new_account->account_mask = null;
                        $new_account->account_type = isset($account['type']) ? $account['type'] : $account['account_type'];
                        $new_account->account_subtype = isset($account['subtype']) ? $account['subtype'] : $account['account_sub_type'];
                        $new_account->user_id = $user_id;
                        //created_by 
                        $new_account->created_by = \Auth::user()->creatorId();
                        $new_account->updated_by = \Auth::user()->ownedId();
                        $new_account->save();
                    } else {
                        $new_account = ([
                            'plaid_item_id' => $obj->item_id,
                            'plaid_access_token' => $obj->access_token,
                            'plaid_public_token' => $request->public_token,
                            'link_session_id' => $request->link_session_id,
                            'link_token' => $request->link_token,
                            'institution_id' => $request->institution['institution_id'],
                            'institution_name' => $request->institution['name'],
                            'account_id' => isset($account['id']) ? $account['id'] : $account['account_id'],
                            'account_name' => isset($account['name']) ? $account['name'] : $account['account_name'],
                            'account_mask' => isset($account['account_number']) ? $account['account_number'] : $account['mask'],
                            'account_mask' => null,
                            'account_type' => isset($account['type']) ? $account['type'] : $account['account_type'],
                            'account_subtype' => isset($account['subtype']) ? $account['subtype'] : $account['account_sub_type'],
                            'user_id' => $user_id,
                            'created_by' => \Auth::user()->creatorId(),
                            'updated_by' => \Auth::user()->ownedId(),
                        ]);
                        BankAccount::create($new_account);
                    }
                }
            });
        } catch (\Exception $e) {
            dd($e->getMessage());
            Log::error('An error occurred linking a Plaid account: ' . $e->getMessage());
            return response()->json([
                'message' => 'An error occurred attempting to link a Plaid account.'
            ], 200);
        }
        return response()->json([
            'message' => 'Successfully linked plaid account.',
            'item_id' => $obj->item_id
        ], 200);
    }
    public function getInvestmentHoldings(Request $request)
    {
        if ($request->itemId != NULL) {
            $account = BankAccount::where('plaid_item_id', $request->itemId)->first();
        }

        if (!isset($plaid)) {
            $plaid = new Plaid(env('PLAID_CLIENT_ID'), env('PLAID_SECRET'), env('PLAID_ENV'));
        }


        try {
            $results = $plaid->investments->listHoldings($account->plaid_access_token);
            $account->last_update = new \DateTime();
            $account->last_status = '';
            $account->save();
        } catch (PlaidRequestException $e) {
            // Plaid-specific errors
            $resp = $e->getResponse();
            $account->last_status = $resp['error_code'] ?? 'UNKNOWN_ERROR';
            $account->save();

            Log::error('Error pulling holdings from Plaid: ' . ($resp['error_code'] ?? 'no_code'), [
                'response' => $resp
            ]);

            // Stop the transaction and return a JSON error
            throw new \RuntimeException('Plaid error: ' . $resp['error_message'] ?? $e->getMessage());
        } catch (\Throwable $e) {
            // Any other exception
            Log::error('Unexpected error pulling holdings: ' . $e->getMessage());
            throw $e;
        }

        // Only proceed if we got results
        foreach ($results->holdings as $holding) {
            Holdings::create([
                'institution_id' => $account->institution_id,
                'holding_id' => $holding->security_id,
                'user_id' => $account->user_id,
                'cost_basis' => $holding->cost_basis,
                'price' => $holding->institution_price,
            ]);
        }


        return [
            'result' => 'success',
            'message' => 'Successfully added holdings from Plaid.'
        ];
    }

    public function showBalance($id)
    {
        $pa = BankAccount::where('institution_id', $id)->firstOrFail();
        $res = $this->plaid
            ->accounts
            ->getBalance($pa->plaid_access_token);

        return view('bankAccount.balance', ['accounts' => $res->accounts]);
        // return response()->json($res->accounts);
    }
    public function getConnectedBank(Request $request)
    {
        $userId = Auth::user()->id;

        $plaidAccounts = BankAccount::where('user_id', $userId)->get();

        if ($plaidAccounts->isEmpty()) {
            return response()->json(['result' => 'error', 'message' => 'No Plaid access tokens found'], 400);
        }

        $banks = [];
        $seenInstitutionIds = [];
        $balance = 0;
        foreach ($plaidAccounts as $pa) {
            try {
                $accessToken = $pa->plaid_access_token;

                // 1. Get item info
                $itemResponse = $this->plaid->items->get($accessToken);
                $institutionId = $itemResponse->item->institution_id;

                // Skip if already added
                if (in_array($institutionId, $seenInstitutionIds)) {
                    continue;
                }

                // 2. Get institution info
                $institutionResponse = $this->plaid->institutions->get($institutionId, ['US']);
                //balance
                $balanceResponse = $this->plaid->accounts->getBalance($accessToken);
                foreach ($balanceResponse->accounts as $account) {
                    $balance += $account->balances->current;
                }
                $banks[] = [
                    'bank_name' => $institutionResponse->institution->name,
                    'institution_id' => ucfirst($institutionId),
                    'total_balance' => \Auth::user()->priceFormat($balance)
                ];

                $seenInstitutionIds[] = $institutionId;

                // if (count($banks) >= 2) {
                //     break;
                // }

            } catch (\TomorrowIdeas\Plaid\Exceptions\PlaidRequestException $e) {
                \Log::warning("Plaid request error: " . $e->getMessage());
                continue;
            } catch (\Throwable $e) {
                \Log::error("Unexpected error: " . $e->getMessage());
                continue;
            }
        }

        return response()->json([
            'result' => 'success',
            'banks' => $banks,
        ]);
    }


}
