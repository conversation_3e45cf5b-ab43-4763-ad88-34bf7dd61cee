<?php

namespace App\Http\Controllers;

use App\Exports\BillExport;
use App\Models\BankAccount;
use App\Models\Bill;
use App\Models\BillAccount;
use App\Models\BillPayment;
use App\Models\BillProduct;
use App\Models\ChartOfAccount;
use App\Models\CustomField;
use App\Models\DebitNote;
use App\Models\JournalEntry;
use App\Models\JournalItem;
use App\Models\ProductService;
use App\Models\ProductServiceCategory;
use App\Models\StockReport;
use App\Models\Transaction;
use App\Models\User;
use App\Models\Utility;
use App\Models\Vender;
use App\Models\WorkFlow;
use App\Models\Notification;
use App\Models\WorkFlowAction;
use App\Models\TransactionLines;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use Auth;

class BillController extends Controller
{

    public function index(Request $request)
    {
        if(\Auth::user()->can('manage bill'))
        {
            $user = \Auth::user();
            $ownerId = $user->type === 'company' ? $user->creatorId() : $user->ownedId();
            $column = ($user->type == 'company') ? 'created_by' : 'owned_by';
            $vender = Vender::where($column, '=',$ownerId)->get()->pluck('name', 'id');
            $vender->prepend('Select Vendor', '');

            $status = Bill::$statues;

            $query = Bill::where('type', '=', 'Bill')->where($column, '=',$ownerId);
            if(!empty($request->vender))
            {
                $query->where('vender_id', '=', $request->vender);
            }
                //            if(!empty($request->bill_date))
                //            {
                //                $date_range = explode('to', $request->bill_date);
                //                $query->whereBetween('bill_date', $date_range);
                //            }

            if(count(explode('to', $request->bill_date)) > 1)
            {
                $date_range = explode(' to ', $request->bill_date);
                $query->whereBetween('bill_date', $date_range);
            }
            elseif(!empty($request->bill_date))
            {
                $date_range = [$request->date , $request->bill_date];
                $query->whereBetween('bill_date', $date_range);
            }

            if ($request->status != null)
            {
                $query->where('status', '=', $request->status);
            }
            $bills = $query->with('category')->get();

            return view('bill.index', compact('bills', 'vender', 'status'));
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    public function create($vendorId)
    {

        if(\Auth::user()->can('create bill'))
        {
            $user = \Auth::user();
            $ownerId = $user->type === 'company' ? $user->creatorId() : $user->ownedId();
            $column = ($user->type == 'company') ? 'created_by' : 'owned_by';
            $customFields = CustomField::where('created_by', '=', \Auth::user()->creatorId())->where('module', '=', 'bill')->get();
            $category     = ProductServiceCategory::where($column, $ownerId)
                ->whereNotIn('type', ['product & service', 'income',])
                ->get()->pluck('name', 'id');
            $category->prepend('Select Category', '');

            $bill_number = \Auth::user()->billNumberFormat($this->billNumber());
            $venders     = Vender::where($column, $ownerId)->get()->pluck('name', 'id');
            $venders->prepend('Select Vender', '');

            $product_services = ProductService::where($column, $ownerId)->get()->pluck('name', 'id');
            $product_services->prepend('Select Item', '');

            $chartAccounts = ChartOfAccount::select(\DB::raw('CONCAT(code, " - ", name) AS code_name, id'))
                ->where('created_by', \Auth::user()->creatorId())->get()
                ->pluck('code_name', 'id');
            $chartAccounts->prepend('Select Account', '');

            $subAccounts = ChartOfAccount::select('chart_of_accounts.id', 'chart_of_accounts.code', 'chart_of_accounts.name' , 'chart_of_account_parents.account');
            $subAccounts->leftjoin('chart_of_account_parents', 'chart_of_accounts.parent', 'chart_of_account_parents.id');
            $subAccounts->where('chart_of_accounts.parent', '!=', 0);
            $subAccounts->where('chart_of_accounts.created_by', \Auth::user()->creatorId());
            $subAccounts = $subAccounts->get()->toArray();

            return view('bill.create', compact('venders', 'bill_number', 'product_services', 'category', 'customFields', 'vendorId','chartAccounts' , 'subAccounts'));
        }
        else
        {
            return response()->json(['error' => __('Permission denied.')], 401);
        }
    }


    public function store(Request $request)
    {
        \DB::beginTransaction();
        try {
        if(\Auth::user()->can('create bill'))
        {

            $validator = \Validator::make(
                $request->all(), [
                    'vender_id' => 'required',
                    'bill_date' => 'required',
                    'due_date' => 'required'
                ]
            );
            if ($validator->fails()) {
                $messages3 = $validator->getMessageBag();
                return redirect()->back()->with('error', $messages3->first());
            }

            if (!empty($request->items) && empty($request->items[0]['item']) && empty($request->items[0]['chart_account_id']) && empty($request->items[0]['amount']))
            {
                $itemValidator = \Validator::make(
                    $request->all(), [
                        'item' => 'required'
                    ]
                );
                if ($itemValidator->fails()) {
                    $messages1 = $itemValidator->getMessageBag();
                    return redirect()->back()->with('error', $messages1->first());
                }
            }

            if (!empty($request->items) && empty($request->items[0]['chart_account_id'])  && !empty($request->items[0]['amount']) )
            {
                $accountValidator = \Validator::make(
                    $request->all(), [
                        'chart_account_id' => 'required'
                    ]
                );
                if ($accountValidator->fails()) {
                    $messages2 = $accountValidator->getMessageBag();
                    return redirect()->back()->with('error', $messages2->first());
                }

            }

            $bill            = new Bill();
            $bill->bill_id   = $this->billNumber();
            $bill->vender_id = $request->vender_id;;
            $bill->bill_date      = $request->bill_date;
            $bill->status         = 0;
            $bill->type         =  'Bill';
            $bill->user_type         =  'vendor';
            $bill->due_date       = $request->due_date;
            $bill->category_id    = !empty($request->category_id) ? $request->category_id :0;
            $bill->order_number   = !empty($request->order_number) ? $request->order_number : 0;
            $bill->created_by     = \Auth::user()->creatorId();
            $bill->owned_by     = \Auth::user()->ownedId();
            $bill->save();

            CustomField::saveData($bill, $request->customField);
            $products = $request->items;
            $newitems = $request->items;

            $total_amount=0;

            for($i = 0; $i < count($products); $i++)
            {
                if(!empty($products[$i]['item']))
                {
                    $billProduct              = new BillProduct();
                    $billProduct->bill_id     = $bill->id;
                    $billProduct->product_id  = $products[$i]['item'];
                    $billProduct->quantity    = $products[$i]['quantity'];
                    $billProduct->tax         = $products[$i]['tax'];
                    $billProduct->discount    = $products[$i]['discount'];
                    $billProduct->price       = $products[$i]['price'];
                    $billProduct->description = $products[$i]['description'];
                    $billProduct->save();
                    $newitems[$i]['prod_id'] = $billProduct->id;
                }

                $billTotal=0;
                if(!empty($products[$i]['chart_account_id'])){
                    $billAccount                    = new BillAccount();
                    $billAccount->chart_account_id  = $products[$i]['chart_account_id'];
                    $billAccount->price             = $products[$i]['amount'] ? $products[$i]['amount'] : 0;
                    $billAccount->description       = $products[$i]['description'];
                    $billAccount->type              = 'Bill';
                    $billAccount->ref_id            = $bill->id;
                    $billAccount->save();
                    $newitems[$i]['bill_account_id'] = $billAccount->id;
                    $billTotal= $billAccount->price;
                }

                //inventory management (Quantity)
                if(!empty($billProduct))
                {
                    Utility::total_quantity('plus',$billProduct->quantity,$billProduct->product_id);
                }

                //Product Stock Report
                if(!empty($products[$i]['item']))
                {
                    $type='bill';
                    $type_id = $bill->id;
                    $description=$products[$i]['quantity'].'  '.__('quantity purchase in bill').' '. \Auth::user()->billNumberFormat($bill->bill_id);
                    Utility::addProductStock( $products[$i]['item'],$products[$i]['quantity'],$type,$description,$type_id);
                    // $total_amount += ($billProduct->quantity * $billProduct->price)+$billTotal ;
                    $total_amount += ((float)$billProduct->quantity * (float)$billProduct->price) + (float)$billTotal;

                }

            }

            if(!empty($request->chart_account_id))
            {

                $billaccount= ProductServiceCategory::find($request->category_id);
                $chart_account = ChartOfAccount::find($billaccount->chart_account_id);
                $billAccount                    = new BillAccount();
                $billAccount->chart_account_id  = $chart_account['id'];
                $billAccount->price             = $total_amount;
                $billAccount->description       = $request->description;
                $billAccount->type              = 'Bill Category';
                $billAccount->ref_id            = $bill->id;
                $billAccount->save();
            }

            // // WorkFlow get which is active
            $us_mail = 'false';
            $us_notify = 'false';
            $us_approve = 'false';
            $usr_Notification = [];
            $workflow = WorkFlow::where('created_by', '=', \Auth::user()->creatorId())->where('module', '=', 'accounts')->where('status', 1)->first();
            if ($workflow) {
                $workflowaction = WorkFlowAction::where('workflow_id', $workflow->id)->where('status', 1)->get();
                foreach ($workflowaction as $action) {
                    $useraction = json_decode($action->assigned_users);
                    if (strtolower('create-bill') == $action->node_id) {
                        // Pick that stage user assign or change on lead
                        if (@$useraction != '') {
                            $useraction = json_decode($useraction);
                            foreach ($useraction as $anyaction) {
                                // make new user array
                                if ($anyaction->type == 'user') {
                                    $usr_Notification[] = $anyaction->id;
                                }
                            }
                        }
                        $raw_json = trim($action->applied_conditions, '"');
                        $cleaned_json = stripslashes($raw_json);
                        $applied_conditions = json_decode($cleaned_json, true);

                        if (isset($applied_conditions['conditions']) && is_array($applied_conditions['conditions'])) {
                            $arr = [
                                'bill_date' => 'bill_date',
                                'due_date' => 'due_date',
                                'order_number' => 'order_number',
                            ];
                            $relate = [
                            ];

                            foreach ($applied_conditions['conditions'] as $conditionGroup) {

                                if (in_array($conditionGroup['action'], ['send_email', 'send_notification', 'send_approval'])) {
                                    $query = Bill::where('id', $bill->id);
                                    foreach ($conditionGroup['conditions'] as $condition) {
                                        $field = $condition['field'];
                                        $operator = $condition['operator'];
                                        $value = $condition['value'];
                                        if (isset($arr[$field], $relate[$arr[$field]])) {
                                            $relatedField = strpos($arr[$field], '_') !== false ? explode('_', $arr[$field], 2)[1] : $arr[$field];
                                            $relation = $relate[$arr[$field]];

                                            // Apply condition to the related model
                                            $query->whereHas($relation, function ($relatedQuery) use ($relatedField, $operator, $value) {
                                                $relatedQuery->where($relatedField, $operator, $value);
                                            });
                                        } else {
                                            // Apply condition directly to the contract model
                                            $query->where($arr[$field], $operator, $value);
                                        }
                                    }
                                    $result = $query->first();

                                    if (!empty($result)) {
                                        if ($conditionGroup['action'] === 'send_email') {
                                            $us_mail = 'true';
                                        } elseif ($conditionGroup['action'] === 'send_notification') {
                                            $us_notify = 'true';
                                        } elseif ($conditionGroup['action'] === 'send_approval') {
                                            $us_approve = 'true';
                                        }
                                    }
                                }
                            }
                        }
                        if ($us_mail == 'true') {
                            // email send
                        }
                        if ($us_notify == 'true' || $us_approve == 'true') {
                            // notification generate
                            if (count($usr_Notification) > 0) {
                                $usr_Notification[] = Auth::user()->creatorId();
                                foreach ($usr_Notification as $usrLead) {
                                    $data = [
                                        "updated_by" => Auth::user()->id,
                                        "data_id" => $bill->id,
                                        "name" => '',
                                    ];
                                    if($us_notify == 'true'){
                                        Utility::makeNotification($usrLead,'create_bill',$data,$bill->id,'create Bill');
                                    }elseif($us_approve == 'true'){
                                        Utility::makeNotification($usrLead,'approve_bill',$data,$bill->id,'For Approval Bill');
                                    }
                                }
                            }
                        }
                    }
                }
            }


            //For Notification
            $setting  = Utility::settings(\Auth::user()->creatorId());
            $vendor = Vender::find($request->vender_id);
            $billNotificationArr = [
                'bill_number' => \Auth::user()->billNumberFormat($bill->bill_id),
                'user_name' => \Auth::user()->name,
                'bill_date' => $bill->bill_date,
                'bill_due_date' => $bill->due_date,
                'vendor_name' => $vendor->name,
            ];
            //Slack Notification
            if(isset($setting['bill_notification']) && $setting['bill_notification'] ==1)
            {
                Utility::send_slack_msg('new_bill', $billNotificationArr);
            }
            //Telegram Notification
            if(isset($setting['telegram_bill_notification']) && $setting['telegram_bill_notification'] ==1)
            {
                Utility::send_telegram_msg('new_bill', $billNotificationArr);
            }
            //Twilio Notification
            if(isset($setting['twilio_bill_notification']) && $setting['twilio_bill_notification'] ==1)
            {
                Utility::send_twilio_msg($vendor->contact,'new_bill', $billNotificationArr);
            }
            $data['id'] = $bill->id;
            $data['no'] = $bill->bill_id;
            $data['date'] = $bill->bill_date;
            $data['created_at'] = date('Y-m-d', strtotime($bill->bill_date)) . ' ' . date('h:i:s');
            $data['reference'] = $bill->ref_number;
            $data['category'] = 'Bill';
            $data['owned_by'] = $bill->owned_by;
            $data['created_by'] = $bill->created_by;
            $data['prod_id'] = $billProduct->product_id;
            $data['items'] = $newitems;
            $data['created_at'] = date('Y-m-d', strtotime($bill->bill_date)) . ' ' . date('h:i:s');
            $dataret  = Utility::jr_exp_entry($data);
            $bill->voucher_id = $dataret;
            $bill->save();
            //webhook
            $module ='New Bill';
            $webhook =  Utility::webhookSetting($module);
            if($webhook)
            {
                $parameter = json_encode($bill);
                $status = Utility::WebhookCall($webhook['url'],$parameter,$webhook['method']);

                if($status == true)
                {
                    Utility::makeActivityLog(\Auth::user()->id,'Bill',$bill->id,'Create Bill',$bill->type);
                    \DB::commit();
                    return redirect()->route('bill.index', $bill->id)->with('success', __('Bill successfully created.'));
                }
                else
                {
                    \DB::commit();
                    return redirect()->back()->with('error', __('Webhook call failed.'));
                }
            }

            Utility::makeActivityLog(\Auth::user()->id,'Bill',$bill->id,'Create Bill',$bill->type);
            \DB::commit();
            return redirect()->route('bill.index', $bill->id)->with('success', __('Bill successfully created.'));
        }
        else
        {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
        } catch (\Exception $e) {
            \DB::rollback();
            return redirect()->back()->with('error', $e);
        }
    }

    function venderNumber()
    {
        $user = \Auth::user();
        $ownerId = $user->type === 'company' ? $user->creatorId() : $user->ownedId();
        $column = ($user->type == 'company') ? 'created_by' : 'owned_by';
        $latest = Vender::where($column, '=',$ownerId)->latest()->first();
        if(!$latest)
        {
            return 1;
        }

        return $latest->customer_id + 1;
    }

    public function show($ids)
    {

        if(\Auth::user()->can('show bill'))
        {
            try {
                $id       = Crypt::decrypt($ids);
            } catch (\Throwable $th) {
                return redirect()->back()->with('error', __('Bill Not Found.'));
            }

            $id   = Crypt::decrypt($ids);
            $bill = Bill::with('debitNote' ,'payments.bankAccount','items.product.unit')->find($id);

            if(!empty($bill) && $bill->created_by == \Auth::user()->creatorId())
            {
                $billPayment = BillPayment::where('bill_id', $bill->id)->first();
                $vendor      = $bill->vender;

                $item      = $bill->items;
                $accounts  = $bill->accounts;
                $items     = [];
                if(!empty($item) && count($item) > 0)
                {
                    foreach ($item as $k=>$val)
                    {
                        if(!empty($accounts[$k]))
                        {
                            $val['chart_account_id']=$accounts[$k]['chart_account_id'];
                            $val['account_id']=$accounts[$k]['id'];
                            $val['amount']=$accounts[$k]['price'];
                        }
                        $items[]=$val;
                    }
                }
                else{

                    foreach ($accounts as $k=>$val){
                        $val1['chart_account_id']=$accounts[$k]['chart_account_id'];
                        $val1['account_id']=$accounts[$k]['id'];
                        $val1['amount']=$accounts[$k]['price'];
                        $items[]=$val1;

                    }
                }

                $bill->customField = CustomField::getData($bill, 'bill');
                $customFields      = CustomField::where('created_by', '=', \Auth::user()->creatorId())->where('module', '=', 'bill')->get();

                return view('bill.view', compact('bill', 'vendor', 'items', 'billPayment', 'customFields'));
            }
            else
            {
                return redirect()->back()->with('error', __('Permission denied.'));
            }
        }
        else
        {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    public function edit($ids)
    {

        if(\Auth::user()->can('edit bill'))
        {
            try {
                $id       = Crypt::decrypt($ids);
            } catch (\Throwable $th) {
                return redirect()->back()->with('error', __('Bill Not Found.'));
            }

            $id       = Crypt::decrypt($ids);
            $bill     = Bill::find($id);

            if(!empty($bill))
            {
                $user = \Auth::user();
                $ownerId = $user->type === 'company' ? $user->creatorId() : $user->ownedId();
                $column = ($user->type == 'company') ? 'created_by' : 'owned_by';
                $category     = ProductServiceCategory::where($column, $ownerId)
                    ->whereNotIn('type', ['product & service', 'income',])
                    ->get()->pluck('name', 'id');
                $category->prepend('Select Category', '');

                $bill_number      = \Auth::user()->billNumberFormat($bill->bill_id);
                $venders          = Vender::where($column, $ownerId)->get()->pluck('name', 'id');
                $product_services = ProductService::where($column, $ownerId)->get()->pluck('name', 'id');

                $bill->customField = CustomField::getData($bill, 'bill');
                $customFields      = CustomField::where('created_by', '=', \Auth::user()->creatorId())->where('module', '=', 'bill')->get();

                $chartAccounts = ChartOfAccount::select(\DB::raw('CONCAT(code, " - ", name) AS code_name, id'))
                    ->where('created_by', \Auth::user()->creatorId())->get()
                    ->pluck('code_name', 'id');
                $chartAccounts->prepend('Select Account', '');

                $subAccounts = ChartOfAccount::select('chart_of_accounts.id', 'chart_of_accounts.code', 'chart_of_accounts.name' , 'chart_of_account_parents.account');
                $subAccounts->leftjoin('chart_of_account_parents', 'chart_of_accounts.parent', 'chart_of_account_parents.id');
                $subAccounts->where('chart_of_accounts.parent', '!=', 0);
                $subAccounts->where('chart_of_accounts.created_by', \Auth::user()->creatorId());
                $subAccounts = $subAccounts->get()->toArray();

                //for item and account show in repeater
                $item      = $bill->items;
                $accounts  = $bill->accounts;
                $items     = [];
                if(!empty($item) && count($item) > 0)
                {
                    foreach ($item as $k=>$val)
                    {
                        if(!empty($accounts[$k]))
                        {
                            $val['chart_account_id']=$accounts[$k]['chart_account_id'];
                            $val['account_id']=$accounts[$k]['id'];
                            $val['amount']=$accounts[$k]['price'];
                        }
                        $items[]=$val;
                    }
                }
                else{
                    foreach ($accounts as $k=>$val){
                        $val1['chart_account_id']=$accounts[$k]['chart_account_id'];
                        $val1['account_id']=$accounts[$k]['id'];
                        $val1['amount']=$accounts[$k]['price'];
                        $items[]=$val1;

                    }
                }
                return view('bill.edit', compact('venders', 'product_services', 'bill', 'bill_number', 'category',
                    'customFields','chartAccounts','items' , 'subAccounts'));
            }
            else{
                return redirect()->back()->with('error', __('Bill Not Found.'));
            }

        }
        else
        {
            return response()->json(['error' => __('Permission denied.')], 401);
        }
    }

    public function update(Request $request, Bill $bill)
    {

        if (\Auth::user()->can('edit bill')) {

            if ($bill->created_by == \Auth::user()->creatorId()) {
                $validator = \Validator::make(
                    $request->all(),
                    [
                        'vender_id' => 'required',
                        'bill_date' => 'required',
                        'due_date' => 'required',
                    ]
                );
                if ($validator->fails()) {
                    $messages = $validator->getMessageBag();

                    return redirect()->route('bill.index')->with('error', $messages->first());
                }
                $bill->vender_id      = $request->vender_id;
                $bill->bill_date      = $request->bill_date;
                $bill->due_date       = $request->due_date;
                $bill->user_type         =  'vendor';
                $bill->order_number   = $request->order_number;
                $bill->category_id    = $request->category_id;
                $bill->save();
                CustomField::saveData($bill, $request->customField);
                $products = $request->items;
                $total_amount=0;

                for ($i = 0; $i < count($products); $i++)
                {
                    $billProduct = BillProduct::find($products[$i]['id']);
                    if ($billProduct == null)
                    {
                        $billProduct             = new BillProduct();
                        $billProduct->bill_id    = $bill->id;

                        if(isset($products[$i]['items']) ) {
                            Utility::total_quantity('plus', $products[$i]['quantity'], $products[$i]['items']);
                        }

                        $updatePrice= ($products[$i]['price']*$products[$i]['quantity'])+($products[$i]['itemTaxPrice'])-($products[$i]['discount']);
                        Utility::updateUserBalance('vendor', $request->vender_id, $updatePrice, 'debit');

                    }
                    else{

                        Utility::total_quantity('minus',$billProduct->quantity,$billProduct->product_id);
                    }

                    if (isset($products[$i]['items'])) {
                        $billProduct->product_id = $products[$i]['items'];
                        $billProduct->quantity    = $products[$i]['quantity'];
                        $billProduct->tax         = $products[$i]['tax'];
                        $billProduct->discount    = $products[$i]['discount'];
                        $billProduct->price       = $products[$i]['price'];
                        $billProduct->description = $products[$i]['description'];
                        $billProduct->save();
                    }


                    $billTotal=0;
                    if(!empty($products[$i]['chart_account_id'])){
                        $billAccount = BillAccount::find($products[$i]['account_id']);

                        if ($billAccount == null) {
                            $billAccount                    = new BillAccount();
                            $billAccount->chart_account_id = $products[$i]['chart_account_id'];
                        }
                        else{
                            $billAccount->chart_account_id = $products[$i]['chart_account_id'];
                        }
                        $billAccount->price             = $products[$i]['amount'] ? $products[$i]['amount'] : 0;
                        $billAccount->description       = $products[$i]['description'];
                        $billAccount->type              = 'Bill';
                        $billAccount->ref_id            = $bill->id;
                        $billAccount->save();
                        $billTotal= $billAccount->price;
                    }

                    if ($products[$i]['id']>0) {
                        Utility::total_quantity('plus',$products[$i]['quantity'],$billProduct->product_id);
                    }

                    //Product Stock Report
                    $type='bill';
                    $type_id = $bill->id;
                    StockReport::where('type','=','bill')->where('type_id','=',$bill->id)->delete();
                    $description=$products[$i]['quantity'].'  '.__(' quantity purchase in bill').' '. \Auth::user()->billNumberFormat($bill->bill_id);

                    if(isset($products[$i]['items']) ){
                        Utility::addProductStock( $products[$i]['items'],$products[$i]['quantity'],$type,$description,$type_id);
                    }

                    $total_amount += ($billProduct->quantity * $billProduct->price)+$billTotal ;

                }

                if(!empty($request->chart_account_id))
                {
                    $billaccount= ProductServiceCategory::find($request->category_id);
                    $chart_account = ChartOfAccount::find($billaccount->chart_account_id);
                    $billAccount                    = new BillAccount();
                    $billAccount->chart_account_id  = $chart_account['id'];
                    $billAccount->price             = $total_amount;
                    $billAccount->description       = $request->description;
                    $billAccount->type              = 'Bill Category';
                    $billAccount->ref_id            = $bill->id;
                    $billAccount->save();
                }

                TransactionLines::where('reference_id',$bill->id)->where('reference','Bill')->delete();
                TransactionLines::where('reference_id',$bill->id)->where('reference','Bill Account')->delete();

                $bill_products = BillProduct::where('bill_id', $bill->id)->get();
                foreach ($bill_products as $bill_product) {
                    $product = ProductService::find($bill_product->product_id);
                    $totalTaxPrice = 0;
                    if($bill_product->tax != null){

                    $taxes = \App\Models\Utility::tax($bill_product->tax);
                    foreach ($taxes as $tax) {
                        $taxPrice = \App\Models\Utility::taxRate($tax->rate, $bill_product->price, $bill_product->quantity, $bill_product->discount);
                        $totalTaxPrice += $taxPrice;
                    }
                }
                    $itemAmount = ($bill_product->price * $bill_product->quantity) - ($bill_product->discount) + $totalTaxPrice;

                    $data = [
                        'account_id' => $product->expense_chartaccount_id,
                        'transaction_type' => 'Debit',
                        'transaction_amount' => $itemAmount,
                        'reference' => 'Bill',
                        'reference_id' => $bill->id,
                        'reference_sub_id' => $product->id,
                        'date' => $bill->bill_date,
                    ];
                    Utility::addTransactionLines($data , 'edit');
                }

                $bill_accounts = BillAccount::where('ref_id', $bill->id)->get();
                foreach ($bill_accounts as $bill_product) {
                    $data = [
                        'account_id' => $bill_product->chart_account_id,
                        'transaction_type' => 'Debit',
                        'transaction_amount' => $bill_product->price,
                        'reference' => 'Bill Account',
                        'reference_id' => $bill_product->ref_id,
                        'reference_sub_id' => $bill_product->id,
                        'date' => $bill->bill_date,
                    ];
                    Utility::addTransactionLines($data , 'edit');
                }
                //utility activity log
                Utility::makeActivityLog(\Auth::user()->id,'Bill',$bill->id,'Update Bill',$bill->type);
                return redirect()->route('bill.index')->with('success', __('Bill successfully updated.'));
            } else {
                return redirect()->back()->with('error', __('Permission denied.'));
            }
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    public function destroy(Bill $bill)
    {
        if(\Auth::user()->can('delete bill'))
        {
            if($bill->created_by == \Auth::user()->creatorId())
            {
                $billpayments = $bill->payments;

                foreach($billpayments as $key => $value)
                {
                    Utility::bankAccountBalance($value->account_id, $value->amount, 'credit');
                    $transaction= Transaction::where('payment_id',$value->id)->first();
                    $transaction->delete();

                    $billpayment = BillPayment::find($value->id)->first();
                    $billpayment->delete();
                    if(@$value->voucher_id != 0 || @$value->voucher_id != null){
                        JournalEntry::where('id',$value->voucher_id)->where('category','Bill')->delete();
                        JournalItem::where('journal',$value->voucher_id)->delete();
                    }
                }

                //log
                Utility::makeActivityLog(\Auth::user()->id,'Bill',$bill->id,'Delete Bill',$bill->type);
                if(@$bill->voucher_id != 0 || @$value->voucher_id != null){
                    JournalEntry::where('id',$bill->voucher_id)->where('category','Bill')->delete();
                    JournalItem::where('journal',$bill->voucher_id)->delete();
                }
                $bill->delete();

                if($bill->vender_id != 0 && $bill->status!=0)
                {
                    Utility::updateUserBalance('vendor', $bill->vender_id, $bill->getDue(), 'credit');
                }
                BillProduct::where('bill_id', '=', $bill->id)->delete();

                BillAccount::where('ref_id', '=', $bill->id)->delete();

                DebitNote::where('bill', '=', $bill->id)->delete();

                TransactionLines::where('product_id',$bill->id)->where('reference','Bill Journal')->delete();
                TransactionLines::where('reference_id',$bill->id)->where('reference','Bill Journal')->delete();
                TransactionLines::where('reference_id',$bill->id)->where('reference','Bill')->delete();
                TransactionLines::where('reference_id',$bill->id)->where('reference','Bill Account')->delete();
                TransactionLines::where('reference_id',$bill->id)->where('reference','Bill Payment')->delete();

                return redirect()->route('bill.index')->with('success', __('Bill successfully deleted.'));
            }
            else
            {
                return redirect()->back()->with('error', __('Permission denied.'));
            }
        }
        else
        {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

    }

    function billNumber()
    {
        $user = \Auth::user();
        $ownerId = $user->type === 'company' ? $user->creatorId() : $user->ownedId();
        $column = ($user->type == 'company') ? 'created_by' : 'owned_by';
        $latest = Bill::where($column, '=', $ownerId)->latest()->first();
        if(!$latest)
        {
            return 1;
        }

        return $latest->bill_id + 1;
    }

    public function product(Request $request)
    {
        $data['product']     = $product = ProductService::find($request->product_id);
        $data['unit']        = !empty($product->unit) ? $product->unit->name : '';
        $data['taxRate']     = $taxRate = !empty($product->tax_id) ? $product->taxRate($product->tax_id) : 0;
        $data['taxes']       = !empty($product->tax_id) ? $product->tax($product->tax_id) : 0;
        $salePrice           = $product->purchase_price;
        $quantity            = 1;
        $taxPrice            = ($taxRate / 100) * ($salePrice * $quantity);
        $data['totalAmount'] = ($salePrice * $quantity);

        return json_encode($data);
    }

    public function productDestroy(Request $request)
    {

        if(\Auth::user()->can('delete bill product'))
        {
            $billProduct=BillProduct::find($request->id);
            $bill=Bill::find($billProduct->bill_id);

            Utility::updateUserBalance('vendor', $bill->vender_id, $request->amount, 'credit');

            $productService = ProductService::find($billProduct->product_id);

            TransactionLines::where('reference_sub_id',$productService->id)->where('reference','Bill')->delete();

            BillProduct::where('id', '=', $request->id)->delete();
            BillAccount::where('id','=',$request->account_id)->delete();
            Utility::makeActivityLog(\Auth::user()->id,'Bill',$billProduct->id,'Delete Bill Products',$billProduct->description);
            return redirect()->back()->with('success', __('Bill product successfully deleted.'));
        }
        else
        {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    public function sent($id)
    {
        if(\Auth::user()->can('send bill'))
        {
            $bill            = Bill::where('id', $id)->first();
            $bill->send_date = date('Y-m-d');
            $bill->status    = 1;
            $bill->save();

            $vender = Vender::where('id', $bill->vender_id)->first();

            $bill->name = !empty($vender) ? $vender->name : '';
            $bill->bill = \Auth::user()->billNumberFormat($bill->bill_id);

            $billId    = Crypt::encrypt($bill->id);
            $bill->url = route('bill.pdf', $billId);
            Utility::updateUserBalance('vendor', $vender->id, $bill->getTotal(), 'debit');


            $vendorArr = [
                'vender_bill_name' => $bill->name,
                'vender_bill_number' =>$bill->bill,
                'vender_bill_url' => $bill->url,

            ];

            $bill_products = BillProduct::where('bill_id', $bill->id)->get();
            foreach ($bill_products as $bill_product) {
                $product = ProductService::find($bill_product->product_id);
                $totalTaxPrice = 0;
                if($bill_product->tax != null)
                {
                    $taxes = \App\Models\Utility::tax($bill_product->tax);
                    foreach ($taxes as $tax) {
                        $taxPrice = \App\Models\Utility::taxRate($tax->rate, $bill_product->price, $bill_product->quantity, $bill_product->discount);
                        $totalTaxPrice += $taxPrice;
                    }
                }

                $itemAmount = ($bill_product->price * $bill_product->quantity) - ($bill_product->discount) + $totalTaxPrice;

                // $data = [
                //     'account_id' => $product->expense_chartaccount_id,
                //     'transaction_type' => 'Debit',
                //     'transaction_amount' => $itemAmount,
                //     'reference' => 'Bill',
                //     'reference_id' => $bill->id,
                //     'reference_sub_id' => $product->id,
                //     'date' => $bill->bill_date,
                // ];
                // Utility::addTransactionLines($data , 'create');
            }

            $bill_accounts = BillAccount::where('ref_id', $bill->id)->get();
            foreach ($bill_accounts as $bill_product) {
                $data = [
                    'account_id' => $bill_product->chart_account_id,
                    'transaction_type' => 'Debit',
                    'transaction_amount' => $bill_product->price,
                    'reference' => 'Bill Account',
                    'reference_id' => $bill_product->ref_id,
                    'reference_sub_id' => $bill_product->id,
                    'date' => $bill->bill_date,
                ];
                Utility::addTransactionLines($data , 'create');
            }

//            dd($vendorArr);
            $resp = Utility::sendEmailTemplate('vender_bill_sent', [$vender->id => $vender->email], $vendorArr);
            return redirect()->back()->with('success', __('Bill successfully sent.') . (($resp['is_success'] == false && !empty($resp['error'])) ? '<br> <span class="text-danger">' . $resp['error'] . '</span>' : ''));
        }
        else
        {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

    }

    public function resent($id)
    {
//        if(\Auth::user()->can('send bill'))
//        {

            // Send Email
            $setings = Utility::settings();

            if($setings['bill_resent'] == 1)
            {
                $bill = Bill::where('id', $id)->first();
                $vender = Vender::where('id', $bill->vender_id)->first();
                $bill->name = !empty($vender) ? $vender->name : '';
                $bill->bill = \Auth::user()->billNumberFormat($bill->bill_id);
                $billId    = Crypt::encrypt($bill->id);
                $bill->url = route('bill.pdf', $billId);
                $billResendArr = [
                    'vender_name'   => $vender->name,
                    'vender_email'  => $vender->email,
                    'bill_name'  => $bill->name,
                    'bill_number'   => $bill->bill,
                    'bill_url' =>$bill->url,
                ];
                $resp = Utility::sendEmailTemplate('bill_resent', [$vender->id => $vender->email], $billResendArr);

            }

            return redirect()->back()->with('success', __('Bill successfully sent.') . (($resp['is_success'] == false && !empty($resp['error'])) ? '<br> <span class="text-danger">' . $resp['error'] . '</span>' : ''));
//        }
//        else
//        {
//            return redirect()->back()->with('error', __('Permission denied.'));
//        }

    }

    public function payment($bill_id)
    {
        if(\Auth::user()->can('create payment bill'))
        {
            $bill    = Bill::where('id', $bill_id)->first();
            $venders = Vender::where('created_by', '=', \Auth::user()->creatorId())->get()->pluck('name', 'id');

            $categories = ProductServiceCategory::where('created_by', '=', \Auth::user()->creatorId())->get()->pluck('name', 'id');
            $accounts   = BankAccount::select('*', \DB::raw("CONCAT(bank_name,' ',holder_name) AS name"))->where('created_by', \Auth::user()->creatorId())->get()->pluck('name', 'id');

            return view('bill.payment', compact('venders', 'categories', 'accounts', 'bill'));
        }
        else
        {
            return redirect()->back()->with('error', __('Permission denied.'));

        }
    }

    public function createPayment(Request $request, $bill_id)
    {
        \DB::beginTransaction();
        try{
            if(\Auth::user()->can('create payment bill'))
            {
                $validator = \Validator::make(
                    $request->all(), [
                                    'date' => 'required',
                                    'amount' => 'required',
                                    'account_id' => 'required',

                                ]
                );
                if($validator->fails())
                {
                    $messages = $validator->getMessageBag();

                    return redirect()->back()->with('error', $messages->first());
                }

                $billPayment                 = new BillPayment();
                $billPayment->bill_id        = $bill_id;
                $billPayment->date           = $request->date;
                $billPayment->amount         = $request->amount;
                $billPayment->account_id     = $request->account_id;
                $billPayment->payment_method = 0;
                $billPayment->reference      = $request->reference;
                $billPayment->description    = $request->description;

                if(!empty($request->add_receipt))
                {
                    //storage limit
                    $image_size = $request->file('add_receipt')->getSize();
                    $result = Utility::updateStorageLimit(\Auth::user()->creatorId(), $image_size);
                    if($result==1)
                    {
                        if($billPayment->add_receipt)
                        {
                            $path = storage_path('uploads/payment' . $billPayment->add_receipt);
                            if(file_exists($path))
                            {
                                \File::delete($path);
                            }
                        }
                        $fileName = time() . "_" . $request->add_receipt->getClientOriginalName();
                        $billPayment->add_receipt = $fileName;
                        $dir        = 'uploads/payment';
                        $path = Utility::upload_file($request,'add_receipt',$fileName,$dir,[]);
                        if($path['flag']==0){
                            return redirect()->back()->with('error', __($path['msg']));
                        }

                    }

                }
                $billPayment->save();

                $bill  = Bill::where('id', $bill_id)->first();
                $due   = $bill->getDue();
                $total = $bill->getTotal();

                if($bill->status == 0)
                {
                    $bill->send_date = date('Y-m-d');
                    $bill->save();
                }

                if($due <= 0)
                {
                    $bill->status = 4;
                    $bill->save();
                }
                else
                {
                    $bill->status = 3;
                    $bill->save();
                }
                $billPayment->user_id    = $bill->vender_id;
                $billPayment->user_type  = 'Vender';
                $billPayment->type       = 'Partial';
                $billPayment->created_by = \Auth::user()->id;
                $billPayment->payment_id = $billPayment->id;
                $billPayment->category   = 'Bill';
                $billPayment->account    = $request->account_id;
                Transaction::addTransaction($billPayment);

                $vender = Vender::where('id', $bill->vender_id)->first();

                $payment         = new BillPayment();
                $payment->name   = $vender['name'];
                $payment->method = '-';
                $payment->date   = \Auth::user()->dateFormat($request->date);
                $payment->amount = \Auth::user()->priceFormat($request->amount);
                $payment->bill   = 'bill ' . \Auth::user()->billNumberFormat($billPayment->bill_id);

    //            Utility::userBalance('vendor', $bill->vender_id, $request->amount, 'debit');
                Utility::updateUserBalance('vendor', $bill->vender_id, $request->amount, 'credit');


                Utility::bankAccountBalance($request->account_id, $request->amount, 'debit');

                // $billPayments = BillPayment::where('bill_id', $bill->id)->get();
                // foreach ($billPayments as $billPayment) {
                //     $accountId = BankAccount::find($billPayment->account_id);

                //     $data = [
                //         'account_id' => $accountId->chart_account_id,
                //         'transaction_type' => 'Debit',
                //         'transaction_amount' => $billPayment->amount,
                //         'reference' => 'Bill Payment',
                //         'reference_id' => $bill->id,
                //         'reference_sub_id' => $billPayment->id,
                //         'date' => $billPayment->date,
                //     ];
                //     Utility::addTransactionLines($data , 'create');
                // }
                 $bankAccount = BankAccount::find($request->account_id);
                    if($bankAccount && $bankAccount->chart_account_id != 0 || $bankAccount->chart_account_id != null){
                        $data['account_id'] = $bankAccount->chart_account_id;
                    }else{
                        return redirect()->back()->with('error', __('Please select chart of account in bank account.'));
                    }

                    $data['id'] = $billPayment->id;
                    $data['no'] = $bill->bill_id;
                    $data['date'] = $billPayment->date;
                    $data['reference'] = $billPayment->reference;
                    $data['description'] = $billPayment->description;
                    $data['amount'] = $billPayment->amount;
                    $data['prod_id'] = $billPayment->id;
                    // $data['result'] = $result;
                    $data['category'] = 'Bill';
                    $data['owned_by'] = $billPayment->owned_by;
                    $data['created_by'] = \Auth::user()->creatorId();
                    $data['created_at'] = date('Y-m-d', strtotime($billPayment->date)) . ' ' . date('h:i:s');


                    if (preg_match('/\bcash\b/i', $bankAccount->bank_name) || preg_match('/\bcash\b/i', $bankAccount->holder_name)) {
                        $dataret  = Utility::cpv_entry($data); // Cash Payment Voucher (CPV)
                    } else {
                        $dataret  = Utility::bpv_entry($data); // Bill Payment Voucher (BPV)
                    }
                    $billPayments = BillPayment::find($billPayment->id);
                    $billPayments->voucher_id = $dataret;
                    $billPayments->save();

                // Send Email
                $setings = Utility::settings();
                if($setings['new_bill_payment'] == 1)
                {

                    $vender = Vender::where('id', $bill->vender_id)->first();
                    $billPaymentArr = [
                        'vender_name'   => $vender->name,
                        'vender_email'  => $vender->email,
                        'payment_name'  =>$payment->name,
                        'payment_amount'=>$payment->amount,
                        'payment_bill'  =>$payment->bill,
                        'payment_date'  =>$payment->date,
                        'payment_method'=>$payment->method,
                        'company_name'=>$payment->method,

                    ];


                    $resp = Utility::sendEmailTemplate('new_bill_payment', [$vender->id => $vender->email], $billPaymentArr);
                    Utility::makeActivityLog(\Auth::user()->id,'Bill apyment',$billPayment->id,'Create Bill apyment',$billPayment->reference);
                    \DB::commit();
                    return redirect()->back()->with('success', __('Payment successfully added.') .((isset($result) && $result!=1) ? '<br> <span class="text-danger">' . $result . '</span>' : '').(($resp['is_success'] == false && !empty($resp['error'])) ? '<br> <span class="text-danger">' . $resp['error'] . '</span>' : '') );

                }
                Utility::makeActivityLog(\Auth::user()->id,'Bill apyment',$billPayment->id,'Create Bill apyment',$billPayment->reference);
                \DB::commit();
                return redirect()->back()->with('success', __('Payment successfully added.'). ((isset($result) && $result!=1) ? '<br> <span class="text-danger">' . $result . '</span>' : ''));


            }
        } catch (\Exception $e) {
            \DB::rollback();
            return redirect()->back()->with('error', $e);
        }
    }

    public function paymentDestroy(Request $request, $bill_id, $payment_id)
    {

        if(\Auth::user()->can('delete payment bill'))
        {
            $payment = BillPayment::find($payment_id);
            BillPayment::where('id', '=', $payment_id)->delete();

            $bill = Bill::where('id', $bill_id)->first();

            $due   = $bill->getDue();
            $total = $bill->getTotal();

            if($due > 0 && $total != $due)
            {
                $bill->status = 3;

            }
            else
            {
                $bill->status = 2;
            }
            TransactionLines::where('reference_sub_id',$payment_id)->where('reference','Bill Payment')->delete();
            if(@$payment->voucher_id != 0 || @$payment->voucher_id != null){
                JournalEntry::where('id',$payment->voucher_id)->where('category','Bill')->delete();
                JournalItem::where('journal',$payment->voucher_id)->delete();
            }

//            Utility::userBalance('vendor', $bill->vender_id, $payment->amount, 'credit');
            Utility::updateUserBalance('vendor', $bill->vender_id, $payment->amount, 'debit');

            Utility::bankAccountBalance($payment->account_id, $payment->amount, 'credit');

            if(!empty($payment->add_receipt))
            {
                //storage limit
                $file_path = '/uploads/payment/'.$payment->add_receipt;
                $result = Utility::changeStorageLimit(\Auth::user()->creatorId(), $file_path);

            }

            $bill->save();
            $type = 'Partial';
            $user = 'Vender';
            Transaction::destroyTransaction($payment_id, $type, $user);
            Utility::makeActivityLog(\Auth::user()->id,'Bill apyment',$payment_id,'Delete Bill apyment',$payment->reference);
            return redirect()->back()->with('success', __('Payment successfully deleted.'));
        }
        else
        {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    public function venderBill(Request $request)
    {
        if(\Auth::user()->can('manage vender bill'))
        {

            $status = Bill::$statues;
            $user = \Auth::user();
            $ownerId = $user->type === 'company' ? $user->creatorId() : $user->ownedId();
            $column = ($user->type == 'company') ? 'created_by' : 'owned_by';
            $query = Bill::where('vender_id', '=', \Auth::user()->vender_id)->where('status', '!=', '0')->where($column, $ownerId);

            if(!empty($request->vender))
            {
                $query->where('id', '=', $request->vender);
            }
            if(!empty($request->bill_date))
            {
                $date_range = explode(' - ', $request->bill_date);
                $query->whereBetween('bill_date', $date_range);
            }

            if(!empty($request->status))
            {
                $query->where('status', '=', $request->status);
            }
            $bills = $query->get();


            return view('bill.index', compact('bills', 'status'));
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    public function venderBillShow($id)
    {
        if(\Auth::user()->can('show bill'))
        {
            $bill_id = Crypt::decrypt($id);
            $bill    = Bill::where('id', $bill_id)->first();

            if($bill->created_by == \Auth::user()->creatorId())
            {
                $vendor = $bill->vender;
                $iteams = $bill->items;

                return view('bill.view', compact('bill', 'vendor', 'iteams'));
            }
            else
            {
                return redirect()->back()->with('error', __('Permission denied.'));
            }
        }
        else
        {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    public function vender(Request $request)
    {
        $vender = Vender::where('id', '=', $request->id)->first();

        return view('bill.vender_detail', compact('vender'));
    }


    public function venderBillSend($bill_id)
    {
        return view('vender.bill_send', compact('bill_id'));
    }

    public function venderBillSendMail(Request $request, $bill_id)
    {
        $validator = \Validator::make(
            $request->all(), [
                               'email' => 'required|email',
                           ]
        );
        if($validator->fails())
        {
            $messages = $validator->getMessageBag();

            return redirect()->back()->with('error', $messages->first());
        }

        $email = $request->email;
        $bill  = Bill::where('id', $bill_id)->first();

        $vender     = Vender::where('id', $bill->vender_id)->first();
        $bill->name = !empty($vender) ? $vender->name : '';
        $bill->bill = \Auth::user()->billNumberFormat($bill->bill_id);

        $billId    = Crypt::encrypt($bill->id);
        $bill->url = route('bill.pdf', $billId);

        try
        {
//            Mail::to($email)->send(new VenderBillSend($bill));
        }
        catch(\Exception $e)
        {
            $smtp_error = __('E-Mail has been not sent due to SMTP configuration');
        }
        //log
        Utility::makeActivityLog(\Auth::user()->id,'Send Mail',$bill->id,'send Bill mail',$bill->reference);
        return redirect()->back()->with('success', __('Bill successfully sent.') . ((isset($smtp_error)) ? '<br> <span class="text-danger">' . $smtp_error . '</span>' : ''));

    }

    public function shippingDisplay(Request $request, $id)
    {
        $bill = Bill::find($id);

        if($request->is_display == 'true')
        {
            $bill->shipping_display = 1;
        }
        else
        {
            $bill->shipping_display = 0;
        }
        $bill->save();
        //log
        Utility::makeActivityLog(\Auth::user()->id,'Shipping address updated',$bill->id,'Shipping address updated',$bill->reference);
        return redirect()->back()->with('success', __('Shipping address status successfully changed.'));
    }

    public function duplicate($bill_id)
    {
        if(\Auth::user()->can('duplicate bill'))
        {
            $bill = Bill::where('id', $bill_id)->first();

            $duplicateBill                   = new Bill();
            $duplicateBill->bill_id          = $this->billNumber();
            $duplicateBill->vender_id        = $bill['vender_id'];
            $duplicateBill->bill_date        = date('Y-m-d');
            $duplicateBill->due_date         = $bill['due_date'];
            $duplicateBill->send_date        = null;
            $duplicateBill->category_id      = $bill['category_id'];
            $duplicateBill->order_number     = $bill['order_number'];
            $duplicateBill->status           = 0;
            $duplicateBill->type             = 'Bill';
            $duplicateBill->shipping_display = $bill['shipping_display'];
            $duplicateBill->created_by       = $bill['created_by'];
            $duplicateBill->save();

            if($duplicateBill)
            {
                $billProduct = BillProduct::where('bill_id', $bill_id)->get();
                foreach($billProduct as $product)
                {
                    $duplicateProduct             = new BillProduct();
                    $duplicateProduct->bill_id    = $duplicateBill->id;
                    $duplicateProduct->product_id = $product->product_id;
                    $duplicateProduct->quantity   = $product->quantity;
                    $duplicateProduct->tax        = $product->tax;
                    $duplicateProduct->discount   = $product->discount;
                    $duplicateProduct->price      = $product->price;
                    $duplicateProduct->save();
                }
            }
            //log
            Utility::makeActivityLog(\Auth::user()->id,'Duplicate Bill',$duplicateBill->id,'Duplicate Bill',$bill->reference);
            return redirect()->back()->with('success', __('Bill duplicate successfully.'));

        }
        else
        {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    public function previewBill($template, $color)
    {
        $objUser  = \Auth::user();
        $settings = Utility::settings();
        $bill     = new Bill();

        $vendor                   = new \stdClass();
        $vendor->email            = '<Email>';
        $vendor->shipping_name    = '<Vendor Name>';
        $vendor->shipping_country = '<Country>';
        $vendor->shipping_state   = '<State>';
        $vendor->shipping_city    = '<City>';
        $vendor->shipping_phone   = '<Vendor Phone Number>';
        $vendor->shipping_zip     = '<Zip>';
        $vendor->shipping_address = '<Address>';
        $vendor->billing_name     = '<Vendor Name>';
        $vendor->billing_country  = '<Country>';
        $vendor->billing_state    = '<State>';
        $vendor->billing_city     = '<City>';
        $vendor->billing_phone    = '<Vendor Phone Number>';
        $vendor->billing_zip      = '<Zip>';
        $vendor->billing_address  = '<Address>';

        $totalTaxPrice = 0;
        $taxesData     = [];
        $items         = [];
        for($i = 1; $i <= 3; $i++)
        {
            $item           = new \stdClass();
            $item->name     = 'Item ' . $i;
            $item->quantity = 1;
            $item->tax      = 5;
            $item->discount = 50;
            $item->price    = 100;
            $item->unit    = 1;

            $taxes = [
                'Tax 1',
                'Tax 2',
            ];

            $itemTaxes = [];
            foreach($taxes as $k => $tax)
            {
                $taxPrice         = 10;
                $totalTaxPrice    += $taxPrice;
                $itemTax['name']  = 'Tax ' . $k;
                $itemTax['rate']  = '10 %';
                $itemTax['price'] = '$10';
                $itemTax['tax_price'] = 10;
                $itemTaxes[]      = $itemTax;
                if(array_key_exists('Tax ' . $k, $taxesData))
                {
                    $taxesData['Tax ' . $k] = $taxesData['Tax 1'] + $taxPrice;
                }
                else
                {
                    $taxesData['Tax ' . $k] = $taxPrice;
                }
            }
            $item->itemTax = $itemTaxes;
            $items[]       = $item;
        }

        $bill->bill_id    = 1;
        $bill->issue_date = date('Y-m-d H:i:s');
        $bill->due_date   = date('Y-m-d H:i:s');
        $bill->itemData   = $items;

        $bill->totalTaxPrice = 60;
        $bill->totalQuantity = 3;
        $bill->totalRate     = 300;
        $bill->totalDiscount = 10;
        $bill->taxesData     = $taxesData;
        $bill->created_by     = $objUser->creatorId();


        $bill->customField   = [];
        $customFields        = [];

        $preview      = 1;
        $color        = '#' . $color;
        $font_color   = Utility::getFontColor($color);

//        $logo         = asset(Storage::url('uploads/logo/'));
//        $bill_logo = Utility::getValByName('bill_logo');
//        $company_logo = \App\Models\Utility::GetLogo();
//        if(isset($bill_logo) && !empty($bill_logo))
//        {
//            $img          = asset(\Storage::url('bill_logo').'/'. $bill_logo);
//        }
//        else
//        {
//            $img          = asset($logo . '/' . (isset($company_logo) && !empty($company_logo) ? $company_logo : 'logo-dark.png'));
//        }

        $logo         = asset(Storage::url('uploads/logo/'));
        $company_logo = Utility::getValByName('company_logo_dark');
        $bill_logo = Utility::getValByName('bill_logo');
        if(isset($bill_logo) && !empty($bill_logo))
        {
            $img = Utility::get_file('bill_logo/') . $bill_logo;
        }
        else{
            $img          = asset($logo . '/' . (isset($company_logo) && !empty($company_logo) ? $company_logo : 'logo-dark.png'));
        }



        return view('bill.templates.' . $template, compact('bill', 'preview', 'color', 'img', 'settings', 'vendor', 'font_color', 'customFields'));
    }

    public function bill($bill_id)
    {
        $settings = Utility::settings();
        try {
            $billId       = Crypt::decrypt($bill_id);
        } catch (\Throwable $th) {
            return redirect()->back()->with('error', __('Bill Not Found.'));
        }
        $billId   = Crypt::decrypt($bill_id);

        $bill  = Bill::where('id', $billId)->first();
        $data  = DB::table('settings');
        $data  = $data->where('created_by', '=', $bill->created_by);
        $data1 = $data->get();

        foreach($data1 as $row)
        {
            $settings[$row->name] = $row->value;
        }

        $vendor = $bill->vender;

        $totalTaxPrice = 0;
        $totalQuantity = 0;
        $totalRate     = 0;
        $totalDiscount = 0;
        $taxesData     = [];
        $items         = [];

        foreach($bill->items as $product)
        {
            $item              = new \stdClass();
            $item->name        = !empty($product->product) ? $product->product->name : '';
            $item->quantity    = $product->quantity;
            $item->unit        = !empty($product->product) ? $product->product->unit_id : '';
            $item->tax         = $product->tax;
            $item->discount    = $product->discount;
            $item->price       = $product->price;
            $item->description = $product->description;

            $totalQuantity += $item->quantity;
            $totalRate     += $item->price;
            $totalDiscount += $item->discount;

            $taxes     = Utility::tax($product->tax);
            $itemTaxes = [];
            if(!empty($item->tax))
            {
                foreach($taxes as $tax)
                {
                    $taxPrice      = Utility::taxRate($tax->rate, $item->price, $item->quantity,$item->discount);
                    $totalTaxPrice += $taxPrice;

                    $itemTax['name']  = $tax->name;
                    $itemTax['rate']  = $tax->rate . '%';
                    $itemTax['price'] = Utility::priceFormat($settings, $taxPrice);
                    $itemTax['tax_price'] =$taxPrice;
                    $itemTaxes[]      = $itemTax;


                    if(array_key_exists($tax->name, $taxesData))
                    {
                        $taxesData[$tax->name] = $taxesData[$tax->name] + $taxPrice;
                    }
                    else
                    {
                        $taxesData[$tax->name] = $taxPrice;
                    }

                }

                $item->itemTax = $itemTaxes;
            }
            else
            {
                $item->itemTax = [];
            }
            $items[] = $item;
        }

        $bill->itemData      = $items;
        $bill->totalTaxPrice = $totalTaxPrice;
        $bill->totalQuantity = $totalQuantity;
        $bill->totalRate     = $totalRate;
        $bill->totalDiscount = $totalDiscount;
        $bill->taxesData     = $taxesData;
        $bill->customField   = CustomField::getData($bill, 'bill');
        $customFields        = [];
        if(!empty(\Auth::user()))
        {
            $customFields = CustomField::where('created_by', '=', \Auth::user()->creatorId())->where('module', '=', 'bill')->get();
        }

//        $logo         = asset(Storage::url('uploads/logo/'));
//        $company_logo = Utility::getValByName('company_logo_dark');
//        $img          = asset($logo . '/' . (isset($company_logo) && !empty($company_logo) ? $company_logo : 'logo-dark.png'));


        $logo         = asset(Storage::url('uploads/logo/'));
        $company_logo = Utility::getValByName('company_logo_dark');
        $settings_data = \App\Models\Utility::settingsById($bill->created_by);
        $bill_logo = $settings_data['bill_logo'];
        if(isset($bill_logo) && !empty($bill_logo))
        {
            $img = Utility::get_file('bill_logo/') . $bill_logo;
        }
        else{
            $img          = asset($logo . '/' . (isset($company_logo) && !empty($company_logo) ? $company_logo : 'logo-dark.png'));
        }

        if($bill)
        {
            $color      = '#' . $settings['bill_color'];
            $font_color = Utility::getFontColor($color);

            return view('bill.templates.' . $settings['bill_template'], compact('bill', 'color', 'settings', 'vendor', 'img', 'font_color', 'customFields'));
        }
        else
        {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

    }

    public function saveBillTemplateSettings(Request $request)
    {
        $post = $request->all();
        unset($post['_token']);

        if(isset($post['bill_template']) && (!isset($post['bill_color']) || empty($post['bill_color'])))
        {
            $post['bill_color'] = "ffffff";
        }


//        $validator = \Validator::make(
//            $request->all(),
//            [
//                'bill_logo' => 'image|mimes:png|max:20480',
//            ]
//        );
//        if($validator->fails())
//        {
//            $messages = $validator->getMessageBag();
//            return  redirect()->back()->with('error', $messages->first());
//        }
//        $bill_logo = \Auth::user()->id . '_bill_logo.png';
//        $path = $request->file('bill_logo')->storeAs('bill_logo', $bill_logo);
//        $post['bill_logo'] = $bill_logo;

        if($request->bill_logo)
        {
            $dir = 'bill_logo/';
            $bill_logo = \Auth::user()->id . '_bill_logo.png';
            $validation =[
                'mimes:'.'png',
                'max:'.'20480',
            ];
            $path = Utility::upload_file($request,'bill_logo',$bill_logo,$dir,$validation);
            if($path['flag']==0)
            {
                return redirect()->back()->with('error', __($path['msg']));
            }
            $post['bill_logo'] = $bill_logo;
        }


        foreach($post as $key => $data)
        {
            \DB::insert(
                'insert into settings (`value`, `name`,`created_by`) values (?, ?, ?) ON DUPLICATE KEY UPDATE `value` = VALUES(`value`) ', [
                                                                                                                                             $data,
                                                                                                                                             $key,
                                                                                                                                             \Auth::user()->creatorId(),
                                                                                                                                         ]
            );
        }

        return redirect()->back()->with('success', __('Bill Setting updated successfully'));
    }

    public function items(Request $request)
    {
        $items = BillProduct::where('bill_id', $request->bill_id)->where('product_id', $request->product_id)->first();
        return json_encode($items);
    }


    public function invoiceLink($billId)
    {
        try {
            $id       = Crypt::decrypt($billId);
        } catch (\Throwable $th) {
            return redirect()->back()->with('error', __('Bill Not Found.'));
        }

        $id             = Crypt::decrypt($billId);
        $bill           =Bill::find($id);
        if(!empty($bill))
        {
            $user_id        = $bill->created_by;
            $user           = User::find($user_id);
            $billPayment = BillPayment::where('bill_id', $bill->id)->get();
            $vendor = $bill->vender;
            $iteams   = $bill->items;
            $bill->customField = CustomField::getData($bill, 'bill');
            $customFields         = CustomField::where('module', '=', 'bill')->get();
            return view('bill.customer_bill', compact('bill', 'vendor', 'iteams', 'customFields','billPayment','user'));
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }

    }

    public function export()
    {
        $name = 'bill_' . date('Y-m-d i:h:s');
        $data = Excel::download(new BillExport(), $name . '.xlsx'); ob_end_clean();

        return $data;
    }

}

